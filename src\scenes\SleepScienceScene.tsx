import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import TextType from '../components/reactbits/textanimations/TextType';
import Aurora from '../components/reactbits/backgrounds/Aurora';
import CountUp from '../components/reactbits/textanimations/CountUp';

const SleepScienceScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 标题滑动到左上角的动画 (90-150帧)
  const titleSlidePhase = interpolate(currentFrame, [90, 150], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题位置和大小变化
  const titleTranslateX = interpolate(titleSlidePhase, [0, 1], [0, -45], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleTranslateY = interpolate(titleSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleScale = interpolate(titleSlidePhase, [0, 1], [1, 0.4], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点动画 - 延后到标题滑动完成后
  const corePointFadeIn = interpolate(currentFrame, [150, 180], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个要点的依次出现 - 在标题滑动完成后开始
  const point1FadeIn = interpolate(currentFrame, [210, 240], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const point2FadeIn = interpolate(currentFrame, [270, 300], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const point3FadeIn = interpolate(currentFrame, [330, 360], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 例子动画
  const exampleFadeIn = interpolate(currentFrame, [420, 450], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const exampleScale = interpolate(currentFrame, [420, 450], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 主标题 - 先居中显示，然后滑动到左上角 */}
      <div
        className="absolute z-20 w-full flex justify-center items-center"
        style={{
          transform: `translate(${titleTranslateX}vw, ${titleTranslateY}vh) scale(${titleScale})`,
          transformOrigin: 'center center',
          transition: 'none',
        }}
      >
        <div className="text-center max-w-4xl px-8">
          <TextType
            text="但睡眠的核心不是早，而是规律"
            className="text-6xl md:text-8xl lg:text-9xl font-semibold text-center leading-tight"
            useFrameBasedAnimation={true}
            startFrame={0}
            duration={90}
            showCursor={true}
            loop={false}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">

        {/* 核心观点 */}
        <div
          className="text-center mb-12"
          style={{ opacity: corePointFadeIn }}
        >
          <div className="text-2xl md:text-3xl text-yellow-300 font-medium mb-4">
            研究表明，成年人平均需要
            <span className="mx-2 text-4xl font-bold text-orange-300">
              <CountUp
                from={0}
                to={7}
                useFrameBasedAnimation={true}
                startFrame={160}
                frameDuration={60}
                ease="easeOut"
                className="inline-block"
              />
              –
              <CountUp
                from={0}
                to={9}
                useFrameBasedAnimation={true}
                startFrame={180}
                frameDuration={60}
                ease="easeOut"
                className="inline-block"
              />
            </span>
            小时的睡眠
          </div>
          <TextType
            text="而最重要的不是几点睡，而是——"
            className="text-xl md:text-2xl text-gray-300"
            useFrameBasedAnimation={true}
            startFrame={180}
            duration={80}
            showCursor={true}
            loop={false}
          />
        </div>

        {/* 三个要点 */}
        <div className="flex-1 flex flex-col justify-center px-16 space-y-8">
          {/* 要点1 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30"
            style={{ opacity: point1FadeIn }}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                <span className="text-white font-bold">1</span>
              </div>
              <p className="text-xl md:text-2xl font-semibold text-blue-200">
                入睡和起床时间是否稳定
              </p>
            </div>
          </div>

          {/* 要点2 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-purple-400/30"
            style={{ opacity: point2FadeIn }}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-4">
                <span className="text-white font-bold">2</span>
              </div>
              <p className="text-xl md:text-2xl font-semibold text-purple-200">
                是否保证足够的睡眠时长
              </p>
            </div>
          </div>

          {/* 要点3 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-indigo-400/30"
            style={{ opacity: point3FadeIn }}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mr-4">
                <span className="text-white font-bold">3</span>
              </div>
              <p className="text-xl md:text-2xl font-semibold text-indigo-200">
                是否获得完整的深睡和快速眼动睡眠
              </p>
            </div>
          </div>
        </div>

        {/* 例子说明 */}
        <div
          className="text-center pb-16 px-8"
          style={{
            opacity: exampleFadeIn,
            transform: `scale(${exampleScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-8 border border-green-400/30 max-w-4xl mx-auto">
            <p className="text-lg md:text-xl text-green-200 mb-2">举个例子：</p>
            <p className="text-xl md:text-2xl font-semibold text-white">
              如果你每天 1 点睡、9 点起，保持规律，
            </p>
            <p className="text-xl md:text-2xl font-semibold text-white mt-2">
              也比"今天 10 点睡，明天 2 点睡"更健康
            </p>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default SleepScienceScene;